import { apiClient } from '@/lib/api-client';
import { useMutation } from '@tanstack/react-query';

type UpdateEmployeePayload = {
  fullName: string;
  email: string;
  phone: string;
  address: string;
  jobTitle: string;
  startDate: string;
};

const updateEmployeeApi = async (id: string, payload: UpdateEmployeePayload) => {
  const response = await apiClient.patch(`/employees/${id}`, payload);
  return response.data;
};

export const useUpdateEmployee = () => {
  return useMutation({
    mutationFn: ({ id, ...payload }: UpdateEmployeePayload & { id: string }) =>
      updateEmployeeApi(id, payload),
  });
};
