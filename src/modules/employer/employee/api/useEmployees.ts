import { apiClient } from '@/lib/api-client';
import { useQuery } from '@tanstack/react-query';

export type EmployeeStatus = 'ACTIVE' | 'INACTIVE' | 'SUSPENDED';
export type KycStatus = 'PENDING' | 'SUBMITTED' | 'VERIFIED' | 'REJECTED';
export type BankStatus = 'PENDING' | 'SUBMITTED' | 'VERIFIED' | 'REJECTED';

export interface Employee {
  id: string;
  fullName: string;
  email: string;
  phone: string;
  address: string;
  jobTitle: string;
  startDate: string;
  status: EmployeeStatus;
  kycStatus: KycStatus;
  bankStatus: BankStatus;
  companyId: string;
  company: {
    id: string;
    name: string;
  };
  user: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface EmployeesResponse {
  statusCode: number;
  message: string;
  data: Employee[];
  meta: {
    total: number;
    lastPage: number;
    currentPage: number;
    perPage: number;
    prev: number | null;
    next: number | null;
  };
  timestamp: string;
  path: string;
  method: string;
}

export interface EmployeesFilters {
  page?: number;
  limit?: number;
  search?: string;
  status?: EmployeeStatus | 'all';
  kycStatus?: KycStatus | 'all';
}

const fetchEmployees = async (filters: EmployeesFilters = {}): Promise<EmployeesResponse> => {
  const params = new URLSearchParams();

  // Add pagination parameters
  if (filters.page) {
    params.append('page', filters.page.toString());
  }
  if (filters.limit) {
    params.append('limit', filters.limit.toString());
  }

  // Add filter parameters
  if (filters.search && filters.search.trim()) {
    params.append('search', filters.search.trim());
  }
  if (filters.status && filters.status !== 'all') {
    params.append('status', filters.status);
  }
  if (filters.kycStatus && filters.kycStatus !== 'all') {
    params.append('kycStatus', filters.kycStatus);
  }

  const queryString = params.toString();
  const url = queryString ? `/employees?${queryString}` : '/employees';

  const response = await apiClient.get(url);

  // The API client's response interceptor returns response.data,
  // so response is already the API response body
  return response as unknown as EmployeesResponse;
};

export const useEmployees = (filters: EmployeesFilters = {}) => {
  return useQuery({
    queryKey: ['employees-list', filters],
    queryFn: () => fetchEmployees(filters),
  });
};
