import { apiClient } from '@/lib/api-client';
import { useMutation } from '@tanstack/react-query';

type CreateEmployeePayload = {
  fullName: string;
  email: string;
  phone: string;
  address: string;
  jobTitle: string;
  startDate: string;
};

const createEmployeeApi = async (payload: CreateEmployeePayload) => {
  const response = await apiClient.post(`/employees`, payload);
  return response.data;
};

export const useCreateEmployee = () => {
  return useMutation({ mutationFn: createEmployeeApi });
};
