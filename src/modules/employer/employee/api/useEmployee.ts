import { apiClient } from '@/lib/api-client';
import { useQuery } from '@tanstack/react-query';
import { Employee } from './useEmployees';

export interface EmployeeResponse {
  statusCode: number;
  message: string;
  data: Employee;
  timestamp: string;
  path: string;
  method: string;
}

const fetchEmployee = async (id: string): Promise<EmployeeResponse> => {
  const response = await apiClient.get(`/employees/${id}`);

  // The API client's response interceptor returns response.data,
  // so response is already the API response body
  return response as unknown as EmployeeResponse;
};

export const useEmployee = (id: string) => {
  return useQuery({
    queryKey: ['employee-details', id],
    queryFn: () => fetchEmployee(id),
    enabled: !!id, // Only run query if id is provided
  });
};
