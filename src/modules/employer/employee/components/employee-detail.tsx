'use client';

import { useState } from 'react';
import {
  ArrowLeft,
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Building2,
  Edit,
  Shield,
  CreditCard,
  CheckCircle,
  XCircle,
  Clock,
  Loader2,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useEmployee } from '../api/useEmployee';
import { useRouter } from 'nextjs-toploader/app';
import { CreateUpdateEmployeeModal } from './create-update-employee-modal';

interface EmployeeDetailProps {
  employeeId: string;
}

export function EmployeeDetail({ employeeId }: EmployeeDetailProps) {
  const router = useRouter();
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  // Fetch employee data using the API
  const { data: employeeResponse, isLoading, error } = useEmployee(employeeId);
  const employee = employeeResponse?.data;

  // Loading state
  if (isLoading) {
    return (
      <div className="flex h-full flex-col items-center justify-center py-20">
        <Loader2 className="text-primary mb-4 h-8 w-8 animate-spin" />
        <h2 className="mb-4 text-2xl font-bold">Loading Employee Details</h2>
        <p className="text-gray-600">Please wait while we fetch the employee information...</p>
      </div>
    );
  }

  // Error state
  if (error || !employee) {
    return (
      <div className="flex h-full flex-col items-center justify-center py-20">
        <h2 className="mb-4 text-2xl font-bold">Employee Not Found</h2>
        <p className="mb-8 text-gray-600">
          The employee you&apos;re looking for doesn&apos;t exist or you don&apos;t have permission
          to view it.
        </p>
        <Button onClick={() => router.back()} className="bg-primary hover:bg-primary/90">
          Back to Employees
        </Button>
      </div>
    );
  }
  const getStatusBadge = (status: string) => {
    const normalizedStatus = status.toLowerCase();
    switch (normalizedStatus) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Active</Badge>;
      case 'inactive':
        return <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-100">Inactive</Badge>;
      case 'suspended':
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">Suspended</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getKycStatusBadge = (status: string) => {
    const normalizedStatus = status.toLowerCase();
    const statusConfig = {
      verified: {
        icon: CheckCircle,
        color: 'text-green-600',
        bg: 'bg-green-100',
        text: 'text-green-800',
      },
      pending: {
        icon: Clock,
        color: 'text-yellow-600',
        bg: 'bg-yellow-100',
        text: 'text-yellow-800',
      },
      submitted: {
        icon: Clock,
        color: 'text-blue-600',
        bg: 'bg-blue-100',
        text: 'text-blue-800',
      },
      rejected: {
        icon: XCircle,
        color: 'text-red-600',
        bg: 'bg-red-100',
        text: 'text-red-800',
      },
    };
    const config =
      statusConfig[normalizedStatus as keyof typeof statusConfig] || statusConfig.pending;
    const Icon = config.icon;
    return (
      <Badge className={`${config.bg} ${config.text} hover:${config.bg} flex items-center gap-1`}>
        <Icon className="h-3 w-3" />
        {normalizedStatus.charAt(0).toUpperCase() + normalizedStatus.slice(1)}
      </Badge>
    );
  };

  const getBankStatusBadge = (status: string) => {
    const normalizedStatus = status.toLowerCase();
    const statusConfig = {
      verified: {
        icon: CheckCircle,
        color: 'text-green-600',
        bg: 'bg-green-100',
        text: 'text-green-800',
      },
      pending: {
        icon: Clock,
        color: 'text-yellow-600',
        bg: 'bg-yellow-100',
        text: 'text-yellow-800',
      },
      submitted: {
        icon: Clock,
        color: 'text-blue-600',
        bg: 'bg-blue-100',
        text: 'text-blue-800',
      },
      rejected: {
        icon: XCircle,
        color: 'text-red-600',
        bg: 'bg-red-100',
        text: 'text-red-800',
      },
    };
    const config =
      statusConfig[normalizedStatus as keyof typeof statusConfig] || statusConfig.pending;
    const Icon = config.icon;
    return (
      <Badge className={`${config.bg} ${config.text} hover:${config.bg} flex items-center gap-1`}>
        <Icon className="h-3 w-3" />
        {normalizedStatus.charAt(0).toUpperCase() + normalizedStatus.slice(1)}
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="icon"
            onClick={() => router.back()}
            className="h-10 w-10 bg-transparent"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight text-gray-900">{employee.fullName}</h1>
            <p className="text-muted-foreground text-lg">
              Employee details and verification status
            </p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <Button
            onClick={() => setIsEditModalOpen(true)}
            className="bg-primary hover:bg-primary/90 h-10 px-6"
          >
            <Edit className="mr-2 h-4 w-4" />
            Edit Employee
          </Button>
        </div>
      </div>
      {/* Employee Overview */}
      <div className="grid gap-6 lg:grid-cols-3">
        {/* Basic Information */}
        <div className="lg:col-span-2">
          <Card className="h-full gap-0">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-3 text-xl">
                <div className="bg-primary/10 flex h-10 w-10 items-center justify-center rounded-lg">
                  <User className="text-primary h-5 w-5" />
                </div>
                Basic Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Status Row */}
              <div className="flex items-center justify-between border-b border-gray-100 py-3">
                <span className="text-sm font-medium text-gray-600">Status</span>
                {getStatusBadge(employee.status)}
              </div>
              {/* Contact Information Grid */}
              <div className="grid gap-6 sm:grid-cols-2">
                <div className="flex items-start gap-3">
                  <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gray-100">
                    <Mail className="h-4 w-4 text-gray-600" />
                  </div>
                  <div className="min-w-0 flex-1">
                    <p className="text-sm font-medium text-gray-900">Email Address</p>
                    <p className="truncate text-sm text-gray-600">{employee.email}</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gray-100">
                    <Phone className="h-4 w-4 text-gray-600" />
                  </div>
                  <div className="min-w-0 flex-1">
                    <p className="text-sm font-medium text-gray-900">Phone Number</p>
                    <p className="text-sm text-gray-600">{employee.phone}</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gray-100">
                    <Building2 className="h-4 w-4 text-gray-600" />
                  </div>
                  <div className="min-w-0 flex-1">
                    <p className="text-sm font-medium text-gray-900">Employer</p>
                    <p className="text-sm text-gray-600">{employee.company.name}</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gray-100">
                    <Calendar className="h-4 w-4 text-gray-600" />
                  </div>
                  <div className="min-w-0 flex-1">
                    <p className="text-sm font-medium text-gray-900">Start Date</p>
                    <p className="text-sm text-gray-600">
                      {new Date(employee.startDate).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric',
                      })}
                    </p>
                  </div>
                </div>
              </div>
              {/* Address - Full Width */}
              <div className="flex items-start gap-3 pt-2">
                <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gray-100">
                  <MapPin className="h-4 w-4 text-gray-600" />
                </div>
                <div className="min-w-0 flex-1">
                  <p className="text-sm font-medium text-gray-900">Address</p>
                  <p className="text-sm leading-relaxed text-gray-600">{employee.address}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
        {/* Profile Summary */}
        <div className="lg:col-span-1">
          <Card className="h-full gap-0">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-3 text-xl">
                <div className="bg-primary/10 flex h-10 w-10 items-center justify-center rounded-lg">
                  <Shield className="text-primary h-5 w-5" />
                </div>
                Verification Status
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Profile Picture */}
              <div className="text-center">
                <Avatar className="mx-auto mb-4 h-20 w-20">
                  <AvatarImage src={`/placeholder.svg?height=80&width=80`} />
                  <AvatarFallback className="bg-primary/10 text-primary text-lg font-medium">
                    {employee.fullName
                      .split(' ')
                      .map((n) => n[0])
                      .join('')}
                  </AvatarFallback>
                </Avatar>
                <h3 className="font-semibold text-gray-900">{employee.fullName}</h3>
                <p className="text-sm text-gray-600">{employee.company.name}</p>
              </div>
              {/* Verification Status */}
              <div className="space-y-4">
                <div className="flex items-center justify-between rounded-lg bg-gray-50 p-3">
                  <div className="flex items-center gap-2">
                    <Shield className="h-4 w-4 text-gray-600" />
                    <span className="text-sm font-medium text-gray-900">KYC Status</span>
                  </div>
                  {getKycStatusBadge(employee.kycStatus)}
                </div>
                <div className="flex items-center justify-between rounded-lg bg-gray-50 p-3">
                  <div className="flex items-center gap-2">
                    <CreditCard className="h-4 w-4 text-gray-600" />
                    <span className="text-sm font-medium text-gray-900">Bank Details</span>
                  </div>
                  {getBankStatusBadge(employee.bankStatus)}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
      {/* Detailed Information Tabs */}
      {/* <Card>
        <CardHeader>
          <CardTitle>Detailed Information</CardTitle>
          <CardDescription>Complete employee profile with KYC and bank details</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="kyc" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="kyc" className="flex items-center gap-2">
                <Shield className="h-4 w-4" />
                KYC Details
              </TabsTrigger>
              <TabsTrigger value="bank" className="flex items-center gap-2">
                <CreditCard className="h-4 w-4" />
                Bank Details
              </TabsTrigger>
            </TabsList>
            <TabsContent value="kyc" className="mt-6 space-y-4">
              <div className="grid gap-6 md:grid-cols-2">
                <Card>
                  <CardHeader className="pb-4">
                    <CardTitle className="flex items-center gap-2 text-lg">
                      <FileText className="h-5 w-5" />
                      Identity Documents
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between rounded-lg border p-3">
                      <div>
                        <p className="font-medium">National ID</p>
                        <p className="text-sm text-gray-600">Government issued ID</p>
                      </div>
                      {employee.kycStatus === 'verified' ? (
                        <CheckCircle className="h-5 w-5 text-green-600" />
                      ) : employee.kycStatus === 'pending' ? (
                        <Clock className="h-5 w-5 text-yellow-600" />
                      ) : (
                        <XCircle className="h-5 w-5 text-red-600" />
                      )}
                    </div>
                    <div className="flex items-center justify-between rounded-lg border p-3">
                      <div>
                        <p className="font-medium">Address Proof</p>
                        <p className="text-sm text-gray-600">Utility bill or bank statement</p>
                      </div>
                      {employee.kycStatus === 'verified' ? (
                        <CheckCircle className="h-5 w-5 text-green-600" />
                      ) : employee.kycStatus === 'pending' ? (
                        <Clock className="h-5 w-5 text-yellow-600" />
                      ) : (
                        <XCircle className="h-5 w-5 text-red-600" />
                      )}
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-4">
                    <CardTitle className="text-lg">Verification Status</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="rounded-lg bg-gray-50 p-6 text-center">
                        {getKycStatusBadge(employee.kycStatus)}
                        <p className="mt-2 text-sm text-gray-600">
                          {employee.kycStatus === 'verified'
                            ? 'All documents verified successfully'
                            : employee.kycStatus === 'pending'
                              ? 'Documents under review'
                              : employee.kycStatus === 'rejected'
                                ? 'Documents need correction'
                                : 'No documents submitted yet'}
                        </p>
                      </div>
                      {employee.kycStatus !== 'verified' && (
                        <Button className="bg-primary hover:bg-primary/90 w-full">
                          {employee.kycStatus === 'not_submitted'
                            ? 'Upload Documents'
                            : 'Review Documents'}
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
            <TabsContent value="bank" className="mt-6 space-y-4">
              <div className="grid gap-6 md:grid-cols-2">
                <Card>
                  <CardHeader className="pb-4">
                    <CardTitle className="flex items-center gap-2 text-lg">
                      <CreditCard className="h-5 w-5" />
                      Bank Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-3">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Account Holder Name</p>
                        <p className="text-gray-900">
                          {employee.bankStatus === 'not_submitted'
                            ? 'Not provided'
                            : employee.fullName}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-600">Bank Name</p>
                        <p className="text-gray-900">
                          {employee.bankStatus === 'not_submitted' ? 'Not provided' : 'Chase Bank'}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-600">Account Number</p>
                        <p className="text-gray-900">
                          {employee.bankStatus === 'not_submitted' ? 'Not provided' : '****1234'}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-600">Routing Number</p>
                        <p className="text-gray-900">
                          {employee.bankStatus === 'not_submitted' ? 'Not provided' : '****5678'}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-4">
                    <CardTitle className="text-lg">Verification Status</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="rounded-lg bg-gray-50 p-6 text-center">
                        {getBankStatusBadge(employee.bankStatus)}
                        <p className="mt-2 text-sm text-gray-600">
                          {employee.bankStatus === 'verified'
                            ? 'Bank details verified successfully'
                            : employee.bankStatus === 'pending'
                              ? 'Bank details under verification'
                              : employee.bankStatus === 'rejected'
                                ? 'Bank details need correction'
                                : 'No bank details submitted yet'}
                        </p>
                      </div>
                      {employee.bankStatus !== 'verified' && (
                        <Button className="bg-primary hover:bg-primary/90 w-full">
                          {employee.bankStatus === 'not_submitted'
                            ? 'Add Bank Details'
                            : 'Review Bank Details'}
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card> */}

      {/* Edit Employee Modal */}
      <CreateUpdateEmployeeModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        mode="edit"
        employeeData={employee}
      />
    </div>
  );
}
