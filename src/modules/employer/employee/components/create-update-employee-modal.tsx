'use client';

import type React from 'react';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useCreateEmployee } from '../api/useCreateEmployee';
import { useUpdateEmployee } from '../api/useUpdateEmployee';
import { toast } from '@/lib/toast';
import { useQueryClient } from '@tanstack/react-query';
import { Employee } from '../api/useEmployees';

const employeeSchema = z.object({
  fullName: z.string().min(1, 'Full name is required'),
  email: z.email('Please enter a valid email address'),
  phone: z.string().min(1, 'Phone number is required'),
  address: z.string().min(1, 'Address is required'),
  jobTitle: z.string().min(1, 'Job title is required'),
  startDate: z.string().min(1, 'Start date is required'),
});

type EmployeeFormData = z.infer<typeof employeeSchema>;

interface CreateUpdateEmployeeModalProps {
  isOpen: boolean;
  onClose: () => void;
  mode: 'create' | 'edit';
  employeeData?: Employee;
}

// Utility function to format ISO date to YYYY-MM-DD for form input
const formatDateForInput = (isoDate: string): string => {
  return isoDate.split('T')[0];
};

export const CreateUpdateEmployeeModal = ({
  isOpen,
  onClose,
  mode,
  employeeData,
}: CreateUpdateEmployeeModalProps) => {
  const queryClient = useQueryClient();
  const [isLoading, setIsLoading] = useState(false);
  const { mutateAsync: createEmployee } = useCreateEmployee();
  const { mutateAsync: updateEmployee } = useUpdateEmployee();

  const form = useForm<EmployeeFormData>({
    resolver: zodResolver(employeeSchema),
    defaultValues: {
      fullName: '',
      email: '',
      phone: '',
      address: '',
      jobTitle: '',
      startDate: '',
    },
  });

  // Effect to populate form when editing
  useEffect(() => {
    if (mode === 'edit' && employeeData) {
      form.reset({
        fullName: employeeData.fullName,
        email: employeeData.email,
        phone: employeeData.phone,
        address: employeeData.address,
        jobTitle: employeeData.jobTitle,
        startDate: formatDateForInput(employeeData.startDate),
      });
    } else if (mode === 'create') {
      form.reset({
        fullName: '',
        email: '',
        phone: '',
        address: '',
        jobTitle: '',
        startDate: '',
      });
    }
  }, [mode, employeeData, form]);

  const handleSubmit = async (data: EmployeeFormData) => {
    setIsLoading(true);

    try {
      if (mode === 'create') {
        await createEmployee({ ...data });
        toast.success('Employee has been created successfully!');
      } else {
        await updateEmployee({ id: employeeData!.id, ...data });
        toast.success('Employee has been updated successfully!');
      }

      form.reset();
      queryClient.invalidateQueries({ queryKey: ['employees-list'] });

      // If editing, also invalidate the specific employee details
      if (mode === 'edit' && employeeData) {
        queryClient.invalidateQueries({ queryKey: ['employee-details', employeeData.id] });
      }

      onClose();
    } catch (error) {
      toast.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    form.reset();
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{mode === 'create' ? 'Add New Employee' : 'Edit Employee'}</DialogTitle>
          <DialogDescription>
            {mode === 'create'
              ? 'Create a new employee account with their basic information.'
              : 'Update the employee information below.'}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="fullName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Full Name</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Enter full name" disabled={isLoading} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type="email"
                        placeholder="Enter email address"
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Enter phone number" disabled={isLoading} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="startDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Start Date</FormLabel>
                    <FormControl>
                      <Input {...field} type="date" disabled={isLoading} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="jobTitle"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Job Title</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="Enter job title" disabled={isLoading} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="address"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Address</FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="Enter full address"
                      rows={3}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end space-x-2 pt-4">
              <Button type="button" variant="outline" onClick={handleClose} disabled={isLoading}>
                Cancel
              </Button>
              <Button
                type="submit"
                className="bg-[#F74464] hover:bg-[#F74464]/90"
                disabled={isLoading}
              >
                {isLoading
                  ? mode === 'create'
                    ? 'Creating...'
                    : 'Updating...'
                  : mode === 'create'
                    ? 'Create Employee'
                    : 'Update Employee'}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};
