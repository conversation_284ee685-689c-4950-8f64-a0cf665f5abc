'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { RepaymentDetail } from '@/components/repayment-detail';
import { AuthGuard } from '@/components/auth/auth-guard';
import { usePermissions } from '@/lib/permissions';

// Sample repayment data (same as in RepaymentManagement)
const repaymentData = [
  {
    id: 'REP001',
    payrollCycle: 'January 2024',
    totalRequests: 12,
    totalRepaymentAmount: 28500,
    status: 'Completed',
    dateRange: 'Jan 1 - Jan 31, 2024',
    processedDate: '2024-02-01',
  },
  {
    id: 'REP002',
    payrollCycle: 'February 2024',
    totalRequests: 8,
    totalRepaymentAmount: 19960,
    status: 'Processing',
    dateRange: 'Feb 1 - Feb 29, 2024',
    processedDate: null,
  },
  {
    id: 'REP003',
    payrollCycle: 'March 2024',
    totalRequests: 15,
    totalRepaymentAmount: 35200,
    status: 'Pending',
    dateRange: 'Mar 1 - Mar 31, 2024',
    processedDate: null,
  },
  {
    id: 'REP004',
    payrollCycle: 'April 2024',
    totalRequests: 6,
    totalRepaymentAmount: 14800,
    status: 'Draft',
    dateRange: 'Apr 1 - Apr 30, 2024',
    processedDate: null,
  },
  {
    id: 'REP005',
    payrollCycle: 'May 2024',
    totalRequests: 10,
    totalRepaymentAmount: 24300,
    status: 'Completed',
    dateRange: 'May 1 - May 31, 2024',
    processedDate: '2024-06-01',
  },
  {
    id: 'REP006',
    payrollCycle: 'June 2024',
    totalRequests: 9,
    totalRepaymentAmount: 21750,
    status: 'Processing',
    dateRange: 'Jun 1 - Jun 30, 2024',
    processedDate: null,
  },
];

export default function RepaymentDetailPage() {
  const router = useRouter();
  const params = useParams();
  const { hasPermission } = usePermissions();
  const [repayment, setRepayment] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const id = params.id as string;
    const foundRepayment = repaymentData.find((r) => r.id === id);
    setRepayment(foundRepayment);
    setLoading(false);
  }, [params.id]);

  const handleBack = () => {
    router.push('/employer/repayments');
  };

  if (!hasPermission('repayments.view_details')) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="text-center">
          <h3 className="mb-2 text-lg font-medium text-gray-900">Access Denied</h3>
          <p className="text-gray-500">You don&apos;t have permission to view repayment details.</p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="text-center">
          <div className="border-primary mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-b-2"></div>
          <p className="text-gray-500">Loading repayment details...</p>
        </div>
      </div>
    );
  }

  if (!repayment) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="text-center">
          <h3 className="mb-2 text-lg font-medium text-gray-900">Repayment Not Found</h3>
          <p className="mb-4 text-gray-500">The requested repayment could not be found.</p>
          <button onClick={handleBack} className="text-primary hover:text-primary/80 font-medium">
            Back to Repayments
          </button>
        </div>
      </div>
    );
  }

  return (
    <AuthGuard allowedRoles={['employer', 'sub_admin']}>
      <RepaymentDetail repayment={repayment} onBack={handleBack} />
    </AuthGuard>
  );
}
