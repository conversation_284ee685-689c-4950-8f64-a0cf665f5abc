'use client';

import { WageManagement } from '@/components/wage-management';
import { AuthGuard } from '@/components/auth/auth-guard';
import { usePermissions } from '@/lib/permissions';

export default function WagesPage() {
  const { hasPermission } = usePermissions();

  if (!hasPermission('wages.view')) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="text-center">
          <h3 className="mb-2 text-lg font-medium text-gray-900">Access Denied</h3>
          <p className="text-gray-500">You don&apos;t have permission to view wages.</p>
        </div>
      </div>
    );
  }

  return (
    <AuthGuard allowedRoles={['employer', 'sub_admin']}>
      <WageManagement />
    </AuthGuard>
  );
}
