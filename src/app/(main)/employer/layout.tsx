'use client';

import type React from 'react';

import { EmployerLayout } from '@/components/layout/employer-layout';
import { usePathname } from 'next/navigation';

// Map of paths to their corresponding titles
const pathTitles: Record<string, string> = {
  '/employer/dashboard': 'Dashboard',
  '/employer/employees': 'Employees',
  '/employer/salary-requests': 'Salary Requests',
  '/employer/wages': 'Wage Management',
  '/employer/repayments': 'Repayments',
  '/employer/role-management': 'Role Management',
};

// For dynamic detail pages
const getDynamicTitle = (pathname: string): string | null => {
  const employeeMatch = pathname.match(/^\/employer\/employees\/(\d+)$/);
  if (employeeMatch) return 'Employee Details';

  const repaymentMatch = pathname.match(/^\/employer\/repayments\/(.+)$/);
  if (repaymentMatch) return 'Repayment Details';

  return null;
};

export default function EmployerDashboardLayout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();

  // Get the title based on the current path, or use a default
  const dynamicTitle = getDynamicTitle(pathname);
  const title = dynamicTitle || pathTitles[pathname] || 'Employer Dashboard';

  return <EmployerLayout title={title}>{children}</EmployerLayout>;
}
