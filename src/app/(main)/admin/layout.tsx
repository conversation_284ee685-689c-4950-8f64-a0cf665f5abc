'use client';

import type React from 'react';

import { AdminLayout } from '@/components/layout/admin-layout';
import { usePathname } from 'next/navigation';

// Map of paths to their corresponding titles
const pathTitles: Record<string, string> = {
  // '/admin/dashboard': 'Dashboard',
  '/admin/employers': 'Employers',
  '/admin/approvals': 'Approvals',
  '/admin/analytics': 'Analytics',
  '/admin/transactions': 'Transactions',
  '/admin/commissions': 'Commissions',
  '/admin/settings': 'Settings',
  '/admin/role-management': 'Role Management',
};

export default function AdminDashboardLayout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();

  // Get the title based on the current path, or use a default
  const title = pathTitles[pathname] || 'Admin Dashboard';

  return <AdminLayout title={title}>{children}</AdminLayout>;
}
