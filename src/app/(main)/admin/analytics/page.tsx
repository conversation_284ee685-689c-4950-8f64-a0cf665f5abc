import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';

export default function AnalyticsPage() {
  return (
    <div className="space-y-6">
      <Tabs defaultValue="overview" className="w-full">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="employers">Employers</TabsTrigger>
          <TabsTrigger value="employees">Employees</TabsTrigger>
          <TabsTrigger value="transactions">Transactions</TabsTrigger>
        </TabsList>
        <TabsContent value="overview" className="mt-6 space-y-6">
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total Employers</CardTitle>
                <CardDescription>All registered employers</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">1,248</div>
                <p className="text-muted-foreground text-xs">+12% from last month</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total Employees</CardTitle>
                <CardDescription>All registered employees</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">23,456</div>
                <p className="text-muted-foreground text-xs">+18% from last month</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total Transactions</CardTitle>
                <CardDescription>All processed transactions</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">$1.2M</div>
                <p className="text-muted-foreground text-xs">+8% from last month</p>
              </CardContent>
            </Card>
          </div>
          <Card>
            <CardHeader>
              <CardTitle>Analytics Overview</CardTitle>
              <CardDescription>Platform performance over time</CardDescription>
            </CardHeader>
            <CardContent className="flex h-[300px] items-center justify-center bg-gray-50 text-gray-500">
              Chart visualization would go here
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="employers" className="mt-6 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Employer Analytics</CardTitle>
              <CardDescription>Detailed employer statistics</CardDescription>
            </CardHeader>
            <CardContent className="flex h-[400px] items-center justify-center bg-gray-50 text-gray-500">
              Employer analytics visualization would go here
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="employees" className="mt-6 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Employee Analytics</CardTitle>
              <CardDescription>Detailed employee statistics</CardDescription>
            </CardHeader>
            <CardContent className="flex h-[400px] items-center justify-center bg-gray-50 text-gray-500">
              Employee analytics visualization would go here
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="transactions" className="mt-6 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Transaction Analytics</CardTitle>
              <CardDescription>Detailed transaction statistics</CardDescription>
            </CardHeader>
            <CardContent className="flex h-[400px] items-center justify-center bg-gray-50 text-gray-500">
              Transaction analytics visualization would go here
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
