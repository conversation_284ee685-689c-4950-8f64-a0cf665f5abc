import type { Metada<PERSON> } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import { QueryProvider } from './QueryProvider';
import { Suspense } from 'react';
import { Toaster } from '@/components/ui/sonner';
import NextTopLoader from 'nextjs-toploader';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Finwage',
  // description: 'Generated by create next app',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <NextTopLoader color="#F74464" showSpinner={false} />
        <QueryProvider>
          <Suspense>{children}</Suspense>
        </QueryProvider>
        <Toaster />
      </body>
    </html>
  );
}
