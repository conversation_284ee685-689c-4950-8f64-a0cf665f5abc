'use client';

import { useState } from 'react';
import {
  ArrowLeft,
  Users,
  Mail,
  Phone,
  Calendar,
  DollarSign,
  Activity,
  Edit,
  Trash2,
  Plus,
  MoreHorizontal,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface EmployerDetailProps {
  employer: any;
  onBack: () => void;
}

export function EmployerDetail({ employer, onBack }: EmployerDetailProps) {
  const [activeTab, setActiveTab] = useState('overview');

  // Mock employee data for this employer
  const employees = [
    {
      id: 'EMP001',
      name: 'Alice Johnson',
      email: '<EMAIL>',
      position: 'Software Engineer',
      department: 'Engineering',
      salary: '$75,000',
      status: 'active',
      joinDate: '2023-03-15',
    },
    {
      id: 'EMP002',
      name: 'Bob Smith',
      email: '<EMAIL>',
      position: 'Product Manager',
      department: 'Product',
      salary: '$85,000',
      status: 'active',
      joinDate: '2023-01-20',
    },
    {
      id: 'EMP003',
      name: 'Carol Davis',
      email: '<EMAIL>',
      position: 'UX Designer',
      department: 'Design',
      salary: '$70,000',
      status: 'inactive',
      joinDate: '2023-05-10',
    },
  ];

  // Mock payroll data
  const payrollHistory = [
    {
      id: 'PAY001',
      period: 'December 2023',
      totalAmount: '$125,000',
      employeeCount: 45,
      status: 'completed',
      processedDate: '2023-12-31',
    },
    {
      id: 'PAY002',
      period: 'November 2023',
      totalAmount: '$123,500',
      employeeCount: 44,
      status: 'completed',
      processedDate: '2023-11-30',
    },
    {
      id: 'PAY003',
      period: 'October 2023',
      totalAmount: '$121,000',
      employeeCount: 43,
      status: 'completed',
      processedDate: '2023-10-31',
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'inactive':
        return 'bg-gray-100 text-gray-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'completed':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" onClick={onBack}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Employers
          </Button>
          <div>
            <h1 className="text-2xl font-bold tracking-tight">{employer.companyName}</h1>
            <p className="text-muted-foreground">Employer ID: {employer.id}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline">
            <Edit className="mr-2 h-4 w-4" />
            Edit Employer
          </Button>
          <Button variant="destructive">
            <Trash2 className="mr-2 h-4 w-4" />
            Delete
          </Button>
        </div>
      </div>

      {/* Employer Info Card */}
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-4">
            <Avatar className="h-16 w-16">
              <AvatarImage src={`/placeholder.svg?height=64&width=64`} />
              <AvatarFallback className="text-lg">
                {employer.companyName.substring(0, 2).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <div className="flex items-center space-x-2">
                <h2 className="text-xl font-semibold">{employer.companyName}</h2>
                <Badge className={getStatusColor(employer.status)}>
                  {employer.status.charAt(0).toUpperCase() + employer.status.slice(1)}
                </Badge>
              </div>
              <p className="text-muted-foreground">{employer.industry}</p>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <div className="flex items-center space-x-2">
              <Users className="text-muted-foreground h-4 w-4" />
              <div>
                <p className="text-sm font-medium">Contact Person</p>
                <p className="text-muted-foreground text-sm">{employer.contactPerson}</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Mail className="text-muted-foreground h-4 w-4" />
              <div>
                <p className="text-sm font-medium">Email</p>
                <p className="text-muted-foreground text-sm">{employer.email}</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Phone className="text-muted-foreground h-4 w-4" />
              <div>
                <p className="text-sm font-medium">Phone</p>
                <p className="text-muted-foreground text-sm">{employer.phone}</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Calendar className="text-muted-foreground h-4 w-4" />
              <div>
                <p className="text-sm font-medium">Join Date</p>
                <p className="text-muted-foreground text-sm">{employer.joinDate}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Employees</CardTitle>
            <Users className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{employer.employees}</div>
            <p className="text-muted-foreground text-xs">+3 from last month</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Monthly Payroll</CardTitle>
            <DollarSign className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{employer.totalPayroll}</div>
            <p className="text-muted-foreground text-xs">+5.2% from last month</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Employees</CardTitle>
            <Activity className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {employees.filter((e) => e.status === 'active').length}
            </div>
            <p className="text-muted-foreground text-xs">
              {Math.round(
                (employees.filter((e) => e.status === 'active').length / employees.length) * 100
              )}
              % of total
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Last Activity</CardTitle>
            <Calendar className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">2 days</div>
            <p className="text-muted-foreground text-xs">{employer.lastActivity}</p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Information Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="employees">Employees</TabsTrigger>
          <TabsTrigger value="payroll">Payroll History</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Company Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium">Industry</label>
                  <p className="text-muted-foreground text-sm">{employer.industry}</p>
                </div>
                <div>
                  <label className="text-sm font-medium">Company Size</label>
                  <p className="text-muted-foreground text-sm">{employer.employees} employees</p>
                </div>
                <div>
                  <label className="text-sm font-medium">Registration Date</label>
                  <p className="text-muted-foreground text-sm">{employer.joinDate}</p>
                </div>
                <div>
                  <label className="text-sm font-medium">Status</label>
                  <Badge className={getStatusColor(employer.status)}>
                    {employer.status.charAt(0).toUpperCase() + employer.status.slice(1)}
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Contact Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium">Primary Contact</label>
                  <p className="text-muted-foreground text-sm">{employer.contactPerson}</p>
                </div>
                <div>
                  <label className="text-sm font-medium">Email Address</label>
                  <p className="text-muted-foreground text-sm">{employer.email}</p>
                </div>
                <div>
                  <label className="text-sm font-medium">Phone Number</label>
                  <p className="text-muted-foreground text-sm">{employer.phone}</p>
                </div>
                <div>
                  <label className="text-sm font-medium">Last Activity</label>
                  <p className="text-muted-foreground text-sm">{employer.lastActivity}</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="employees" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Employee List</CardTitle>
                  <CardDescription>All employees under {employer.companyName}</CardDescription>
                </div>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Employee
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Employee</TableHead>
                    <TableHead>Position</TableHead>
                    <TableHead>Department</TableHead>
                    <TableHead>Salary</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Join Date</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {employees.map((employee) => (
                    <TableRow key={employee.id}>
                      <TableCell>
                        <div className="flex items-center space-x-3">
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={`/placeholder.svg?height=32&width=32`} />
                            <AvatarFallback>
                              {employee.name
                                .split(' ')
                                .map((n) => n[0])
                                .join('')}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-medium">{employee.name}</div>
                            <div className="text-muted-foreground text-sm">{employee.email}</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{employee.position}</TableCell>
                      <TableCell>{employee.department}</TableCell>
                      <TableCell className="font-medium">{employee.salary}</TableCell>
                      <TableCell>
                        <Badge className={getStatusColor(employee.status)}>
                          {employee.status.charAt(0).toUpperCase() + employee.status.slice(1)}
                        </Badge>
                      </TableCell>
                      <TableCell>{employee.joinDate}</TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem>View Details</DropdownMenuItem>
                            <DropdownMenuItem>Edit Employee</DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className="text-red-600">
                              Remove Employee
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="payroll" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Payroll History</CardTitle>
              <CardDescription>
                Monthly payroll processing history for {employer.companyName}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Period</TableHead>
                    <TableHead>Total Amount</TableHead>
                    <TableHead>Employee Count</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Processed Date</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {payrollHistory.map((payroll) => (
                    <TableRow key={payroll.id}>
                      <TableCell className="font-medium">{payroll.period}</TableCell>
                      <TableCell className="font-medium">{payroll.totalAmount}</TableCell>
                      <TableCell>{payroll.employeeCount}</TableCell>
                      <TableCell>
                        <Badge className={getStatusColor(payroll.status)}>
                          {payroll.status.charAt(0).toUpperCase() + payroll.status.slice(1)}
                        </Badge>
                      </TableCell>
                      <TableCell>{payroll.processedDate}</TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem>View Details</DropdownMenuItem>
                            <DropdownMenuItem>Download Report</DropdownMenuItem>
                            <DropdownMenuItem>Export Data</DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Employer Settings</CardTitle>
              <CardDescription>
                Manage settings and preferences for {employer.companyName}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium">Account Status</h4>
                  <p className="text-muted-foreground text-sm">Current status: {employer.status}</p>
                  <div className="mt-2">
                    <Button variant="outline" size="sm">
                      Change Status
                    </Button>
                  </div>
                </div>
                <div>
                  <h4 className="text-sm font-medium">Notifications</h4>
                  <p className="text-muted-foreground text-sm">
                    Configure notification preferences
                  </p>
                  <div className="mt-2">
                    <Button variant="outline" size="sm">
                      Manage Notifications
                    </Button>
                  </div>
                </div>
                <div>
                  <h4 className="text-sm font-medium">Data Export</h4>
                  <p className="text-muted-foreground text-sm">Export employer and employee data</p>
                  <div className="mt-2">
                    <Button variant="outline" size="sm">
                      Export Data
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
