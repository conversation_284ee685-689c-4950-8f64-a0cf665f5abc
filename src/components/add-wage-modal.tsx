'use client';

import type React from 'react';
import { useState } from 'react';
import { DollarSign, Calendar } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

// Sample employee data for selection
const employeeOptions = [
  {
    id: 1,
    fullName: '<PERSON>',
    email: '<EMAIL>',
  },
  {
    id: 2,
    fullName: '<PERSON>',
    email: '<EMAIL>',
  },
  {
    id: 3,
    fullName: '<PERSON>',
    email: '<EMAIL>',
  },
  {
    id: 4,
    fullName: '<PERSON>',
    email: 'david.wils<PERSON>@example.com',
  },
  {
    id: 5,
    fullName: '<PERSON>',
    email: '<EMAIL>',
  },
  {
    id: 6,
    fullName: '<PERSON>',
    email: '<EMAIL>',
  },
  {
    id: 7,
    fullName: 'Grace Lee',
    email: '<EMAIL>',
  },
  {
    id: 8,
    fullName: 'Henry Taylor',
    email: '<EMAIL>',
  },
];

interface AddWageModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function AddWageModal({ isOpen, onClose }: AddWageModalProps) {
  const [formData, setFormData] = useState({
    employeeId: '',
    amount: '',
    date: '',
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.employeeId) {
      newErrors.employeeId = 'Please select an employee';
    }

    if (!formData.amount) {
      newErrors.amount = 'Amount is required';
    } else if (isNaN(Number(formData.amount)) || Number(formData.amount) <= 0) {
      newErrors.amount = 'Please enter a valid amount';
    }

    if (!formData.date) {
      newErrors.date = 'Date is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (validateForm()) {
      setIsSubmitting(true);
      try {
        // Simulate API call
        await new Promise((resolve) => setTimeout(resolve, 1000));

        // Here you would typically send the data to your backend
        console.log('Adding wage:', {
          ...formData,
          amount: Number(formData.amount),
        });

        // Reset form and close modal
        setFormData({
          employeeId: '',
          amount: '',
          date: '',
        });
        setErrors({});
        onClose();
      } catch (error) {
        console.error('Error adding wage:', error);
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  const handleClose = () => {
    setFormData({
      employeeId: '',
      amount: '',
      date: '',
    });
    setErrors({});
    onClose();
  };

  // Get today's date in YYYY-MM-DD format for max date
  const today = new Date().toISOString().split('T')[0];

  // Get selected employee details
  const selectedEmployee = employeeOptions.find((emp) => emp.id.toString() === formData.employeeId);

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <DollarSign className="text-primary h-5 w-5" />
            Add New Wage
          </DialogTitle>
          <DialogDescription>Add a new wage entry for an employee.</DialogDescription>
        </DialogHeader>

        {/* Selected Employee Preview */}
        {selectedEmployee && (
          <div className="mb-4 rounded-lg bg-gray-50 p-4">
            <div className="flex items-center gap-3">
              <Avatar className="h-12 w-12">
                <AvatarImage src={`/placeholder.svg?height=48&width=48`} />
                <AvatarFallback className="bg-primary/10 text-primary font-medium">
                  {selectedEmployee.fullName
                    .split(' ')
                    .map((n: string) => n[0])
                    .join('')}
                </AvatarFallback>
              </Avatar>
              <div>
                <h3 className="font-semibold text-gray-900">{selectedEmployee.fullName}</h3>
                <p className="text-sm text-gray-600">{selectedEmployee.email}</p>
              </div>
            </div>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Employee Selection */}
          <div className="space-y-2">
            <Label htmlFor="employee">Employee *</Label>
            <Select
              value={formData.employeeId}
              onValueChange={(value) => handleInputChange('employeeId', value)}
            >
              <SelectTrigger className={`${errors.employeeId ? 'border-red-500' : ''}`}>
                <SelectValue placeholder="Select an employee" />
              </SelectTrigger>
              <SelectContent>
                {employeeOptions.map((employee) => (
                  <SelectItem key={employee.id} value={employee.id.toString()}>
                    <div className="flex items-center gap-3">
                      <Avatar className="h-6 w-6">
                        <AvatarImage src={`/placeholder.svg?height=24&width=24`} />
                        <AvatarFallback className="bg-primary/10 text-primary text-xs">
                          {employee.fullName
                            .split(' ')
                            .map((n) => n[0])
                            .join('')}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium">{employee.fullName}</div>
                        <div className="text-xs text-gray-600">{employee.email}</div>
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.employeeId && <p className="text-sm text-red-600">{errors.employeeId}</p>}
          </div>

          {/* Amount */}
          <div className="space-y-2">
            <Label htmlFor="amount">Amount *</Label>
            <div className="relative">
              <DollarSign className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 text-gray-400" />
              <Input
                id="amount"
                type="number"
                step="0.01"
                min="0"
                placeholder="0.00"
                value={formData.amount}
                onChange={(e) => handleInputChange('amount', e.target.value)}
                className={`pl-10 ${errors.amount ? 'border-red-500' : ''}`}
              />
            </div>
            {errors.amount && <p className="text-sm text-red-600">{errors.amount}</p>}
          </div>

          {/* Date */}
          <div className="space-y-2">
            <Label htmlFor="date">Date *</Label>
            <div className="relative">
              <Calendar className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 text-gray-400" />
              <Input
                id="date"
                type="date"
                max={today}
                value={formData.date}
                onChange={(e) => handleInputChange('date', e.target.value)}
                className={`pl-10 ${errors.date ? 'border-red-500' : ''}`}
              />
            </div>
            {errors.date && <p className="text-sm text-red-600">{errors.date}</p>}
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-4">
            <Button type="button" variant="outline" onClick={handleClose} disabled={isSubmitting}>
              Cancel
            </Button>
            <Button
              type="submit"
              className="bg-primary hover:bg-primary/90"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Adding...' : 'Add Wage'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
