'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  ROLE_PERMISSIONS,
  type Role,
  type Permission,
  RoleManager,
  usePermissions,
} from '@/lib/permissions';
import { Users, Shield, Settings, Plus } from 'lucide-react';

// Mock user data - in real app this would come from API
const mockUsers = [
  {
    id: '1',
    name: 'John Admin',
    email: '<EMAIL>',
    role: 'admin' as Role,
  },
  {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'sub_admin' as Role,
  },
  {
    id: '3',
    name: '<PERSON>loyer',
    email: '<EMAIL>',
    role: 'employer' as Role,
  },
];

export function RoleManagerComponent() {
  const [users, setUsers] = useState(mockUsers);
  const [selectedRole, setSelectedRole] = useState<Role>('sub_admin');
  const [customPermissions, setCustomPermissions] = useState<Permission[]>(
    ROLE_PERMISSIONS.sub_admin
  );
  const { hasPermission } = usePermissions();

  // Check if current user can manage roles
  if (!hasPermission('users.manage')) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-gray-500">
            <Shield className="mx-auto mb-4 h-12 w-12 text-gray-400" />
            <p>You don&apos;t have permission to manage roles.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const handleRoleChange = (userId: string, newRole: Role) => {
    setUsers(users.map((user) => (user.id === userId ? { ...user, role: newRole } : user)));
    RoleManager.assignRole(userId, newRole);
  };

  const handlePermissionToggle = (permission: Permission) => {
    setCustomPermissions((prev) =>
      prev.includes(permission) ? prev.filter((p) => p !== permission) : [...prev, permission]
    );
  };

  const saveCustomPermissions = () => {
    RoleManager.updateRolePermissions(selectedRole, customPermissions);
    // In a real app, this would make an API call
    console.log(`Updated permissions for ${selectedRole}:`, customPermissions);
  };

  const getRoleBadgeVariant = (role: Role) => {
    switch (role) {
      case 'super_admin':
        return 'destructive';
      case 'sub_admin':
        return 'secondary';
      case 'employer':
        return 'default';
      default:
        return 'outline';
    }
  };

  const allPermissions: Permission[] = [
    'dashboard.view',
    'employers.view',
    'employers.create',
    'employers.edit',
    'employers.delete',
    'employees.view',
    'employees.create',
    'employees.edit',
    'employees.delete',
    'approvals.view',
    'approvals.approve',
    'approvals.reject',
    'analytics.view',
    'transactions.view',
    'transactions.manage',
    'commissions.view',
    'commissions.manage',
    'salary_requests.view',
    'salary_requests.approve',
    'salary_requests.reject',
    'settings.view',
    'settings.manage',
    'users.manage',
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Role Management</h2>
          <p className="text-gray-600">Manage user roles and permissions</p>
        </div>
      </div>

      <Tabs defaultValue="users" className="space-y-4">
        <TabsList>
          <TabsTrigger value="users">User Roles</TabsTrigger>
          <TabsTrigger value="permissions">Role Permissions</TabsTrigger>
          <TabsTrigger value="custom">Custom Roles</TabsTrigger>
        </TabsList>

        <TabsContent value="users" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                User Role Assignment
              </CardTitle>
              <CardDescription>Assign roles to users in the system</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {users.map((user) => (
                  <div
                    key={user.id}
                    className="flex items-center justify-between rounded-lg border p-4"
                  >
                    <div className="flex items-center gap-3">
                      <div className="bg-primary flex h-10 w-10 items-center justify-center rounded-full font-medium text-white">
                        {user.name
                          .split(' ')
                          .map((n) => n[0])
                          .join('')}
                      </div>
                      <div>
                        <p className="font-medium">{user.name}</p>
                        <p className="text-sm text-gray-500">{user.email}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <Badge variant={getRoleBadgeVariant(user.role)}>
                        {user.role.replace('_', ' ').toUpperCase()}
                      </Badge>
                      <Select
                        value={user.role}
                        onValueChange={(value: Role) => handleRoleChange(user.id, value)}
                      >
                        <SelectTrigger className="w-32">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="admin">Admin</SelectItem>
                          <SelectItem value="sub_admin">Sub Admin</SelectItem>
                          <SelectItem value="employer">Employer</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="permissions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Role Permissions
              </CardTitle>
              <CardDescription>View and modify permissions for each role</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-4">
                  <Label htmlFor="role-select">Select Role:</Label>
                  <Select
                    value={selectedRole}
                    onValueChange={(value: Role) => {
                      setSelectedRole(value);
                      setCustomPermissions(ROLE_PERMISSIONS[value]);
                    }}
                  >
                    <SelectTrigger className="w-48">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="admin">Admin</SelectItem>
                      <SelectItem value="sub_admin">Sub Admin</SelectItem>
                      <SelectItem value="employer">Employer</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
                  {allPermissions.map((permission) => (
                    <div key={permission} className="flex items-center space-x-2">
                      <Checkbox
                        id={permission}
                        checked={customPermissions.includes(permission)}
                        onCheckedChange={() => handlePermissionToggle(permission)}
                        disabled={selectedRole === 'super_admin'} // Admin always has all permissions
                      />
                      <Label
                        htmlFor={permission}
                        className="text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        {permission.replace(/[._]/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase())}
                      </Label>
                    </div>
                  ))}
                </div>

                <div className="flex justify-end pt-4">
                  <Button onClick={saveCustomPermissions} disabled={selectedRole === 'super_admin'}>
                    Save Permissions
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="custom" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Custom Roles
              </CardTitle>
              <CardDescription>Create and manage custom roles (Future Feature)</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="py-12 text-center">
                <Settings className="mx-auto mb-4 h-16 w-16 text-gray-400" />
                <h3 className="mb-2 text-lg font-medium text-gray-900">Custom Roles</h3>
                <p className="mb-6 text-gray-500">
                  This feature will allow you to create custom roles with specific permission sets.
                </p>
                <Button disabled>
                  <Plus className="mr-2 h-4 w-4" />
                  Create Custom Role
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
