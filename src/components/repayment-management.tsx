'use client';

import { useState } from 'react';
import { Search, Eye, Download, Calendar, DollarSign, FileText, TrendingUp } from 'lucide-react';
import type { DateRange } from 'react-day-picker';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { DatePickerWithRange } from './ui/date-range-picker';
import { useRouter } from 'next/navigation';
import { usePermissions } from '@/lib/permissions';

// Sample repayment data
const repaymentData = [
  {
    id: 'REP001',
    payrollCycle: 'January 2024',
    totalRequests: 12,
    totalRepaymentAmount: 28500,
    status: 'Completed',
    dateRange: 'Jan 1 - Jan 31, 2024',
    processedDate: '2024-02-01',
  },
  {
    id: 'REP002',
    payrollCycle: 'February 2024',
    totalRequests: 8,
    totalRepaymentAmount: 19960,
    status: 'Processing',
    dateRange: 'Feb 1 - Feb 29, 2024',
    processedDate: null,
  },
  {
    id: 'REP003',
    payrollCycle: 'March 2024',
    totalRequests: 15,
    totalRepaymentAmount: 35200,
    status: 'Pending',
    dateRange: 'Mar 1 - Mar 31, 2024',
    processedDate: null,
  },
  {
    id: 'REP004',
    payrollCycle: 'April 2024',
    totalRequests: 6,
    totalRepaymentAmount: 14800,
    status: 'Draft',
    dateRange: 'Apr 1 - Apr 30, 2024',
    processedDate: null,
  },
  {
    id: 'REP005',
    payrollCycle: 'May 2024',
    totalRequests: 10,
    totalRepaymentAmount: 24300,
    status: 'Completed',
    dateRange: 'May 1 - May 31, 2024',
    processedDate: '2024-06-01',
  },
  {
    id: 'REP006',
    payrollCycle: 'June 2024',
    totalRequests: 9,
    totalRepaymentAmount: 21750,
    status: 'Processing',
    dateRange: 'Jun 1 - Jun 30, 2024',
    processedDate: null,
  },
];

export function RepaymentManagement() {
  const router = useRouter();
  const { hasPermission } = usePermissions();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [dateRange, setDateRange] = useState<DateRange | undefined>();
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Calculate dashboard statistics
  const totalRepaymentAmount = repaymentData.reduce(
    (sum, item) => sum + item.totalRepaymentAmount,
    0
  );
  const totalRequests = repaymentData.reduce((sum, item) => sum + item.totalRequests, 0);
  const completedCycles = repaymentData.filter((item) => item.status === 'Completed').length;
  const pendingAmount = repaymentData
    .filter((item) => item.status !== 'Completed')
    .reduce((sum, item) => sum + item.totalRepaymentAmount, 0);

  // Filter repayments based on search term and status
  const filteredRepayments = repaymentData.filter((repayment) => {
    const matchesSearch = repayment.payrollCycle.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus =
      statusFilter === 'all' || repayment.status.toLowerCase() === statusFilter.toLowerCase();
    return matchesSearch && matchesStatus;
  });

  // Pagination calculations
  const totalItems = filteredRepayments.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentRepayments = filteredRepayments.slice(startIndex, endIndex);

  const handleViewRepayment = (repayment: any) => {
    if (hasPermission('repayments.view_details')) {
      router.push(`/employer/repayments/${repayment.id}`);
    }
  };

  const handleExportReport = () => {
    if (hasPermission('repayments.export')) {
      // Export functionality would be implemented here
      console.log('Exporting repayment report...');
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Repayment Management</h2>
          <p className="text-muted-foreground text-sm">
            Track and manage salary advance repayments by payroll cycle
          </p>
        </div>
        {hasPermission('repayments.export') && (
          <Button onClick={handleExportReport} className="bg-primary hover:bg-primary/90">
            <Download className="mr-2 h-4 w-4" />
            Export Report
          </Button>
        )}
      </div>

      {/* Dashboard Statistics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Repayment Amount</CardTitle>
            <DollarSign className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${totalRepaymentAmount.toLocaleString()}</div>
            <p className="text-muted-foreground text-xs">
              <span className="flex items-center text-green-600">
                <TrendingUp className="mr-1 h-3 w-3" />
                All cycles combined
              </span>
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Requests</CardTitle>
            <FileText className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalRequests}</div>
            <p className="text-muted-foreground text-xs">
              <span className="flex items-center text-blue-600">
                <TrendingUp className="mr-1 h-3 w-3" />
                Salary advance requests
              </span>
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed Cycles</CardTitle>
            <Calendar className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{completedCycles}</div>
            <p className="text-muted-foreground text-xs">
              <span className="flex items-center text-green-600">
                <TrendingUp className="mr-1 h-3 w-3" />
                Successfully processed
              </span>
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Amount</CardTitle>
            <DollarSign className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${pendingAmount.toLocaleString()}</div>
            <p className="text-muted-foreground text-xs">
              <span className="flex items-center text-orange-600">
                <TrendingUp className="mr-1 h-3 w-3" />
                Awaiting processing
              </span>
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle>Repayment Cycles</CardTitle>
          <CardDescription>View and manage repayments organized by payroll cycles</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="mb-6 flex flex-col gap-4 sm:flex-row">
            <div className="relative flex-1">
              <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 text-gray-400" />
              <Input
                placeholder="Search by payroll cycle..."
                value={searchTerm}
                onChange={(e) => {
                  setSearchTerm(e.target.value);
                  setCurrentPage(1);
                }}
                className="pl-10"
              />
            </div>
            <Select
              value={statusFilter}
              onValueChange={(value) => {
                setStatusFilter(value);
                setCurrentPage(1);
              }}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="processing">Processing</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
              </SelectContent>
            </Select>
            <DatePickerWithRange date={dateRange} onDateChange={setDateRange} />
            <Select
              value={itemsPerPage.toString()}
              onValueChange={(value) => {
                setItemsPerPage(Number.parseInt(value));
                setCurrentPage(1);
              }}
            >
              <SelectTrigger className="w-[120px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="5">5 rows</SelectItem>
                <SelectItem value="10">10 rows</SelectItem>
                <SelectItem value="20">20 rows</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {currentRepayments.length === 0 ? (
            <div className="py-8 text-center">
              <Calendar className="mx-auto mb-4 h-12 w-12 text-gray-400" />
              <h3 className="mb-2 text-lg font-medium text-gray-900">No repayments found</h3>
              <p className="text-gray-500">
                {searchTerm || statusFilter !== 'all'
                  ? 'Try adjusting your search criteria or filters'
                  : 'No repayment cycles available'}
              </p>
            </div>
          ) : (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Payroll Cycle</TableHead>
                    <TableHead>Total Requests</TableHead>
                    <TableHead>Total Repayment Amount</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Date Range</TableHead>
                    <TableHead>Processed Date</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {currentRepayments.map((repayment) => (
                    <TableRow key={repayment.id}>
                      <TableCell>
                        <div className="font-medium">{repayment.payrollCycle}</div>
                        <div className="text-muted-foreground text-sm">ID: {repayment.id}</div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <FileText className="h-4 w-4 text-blue-600" />
                          <span className="font-medium">{repayment.totalRequests}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <span className="font-semibold text-green-600">
                          ${repayment.totalRepaymentAmount.toLocaleString()}
                        </span>
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            repayment.status === 'Completed'
                              ? 'default'
                              : repayment.status === 'Processing'
                                ? 'secondary'
                                : repayment.status === 'Pending'
                                  ? 'destructive'
                                  : 'outline'
                          }
                        >
                          {repayment.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-gray-400" />
                          <span className="text-sm">{repayment.dateRange}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        {repayment.processedDate ? (
                          <span className="text-sm">{repayment.processedDate}</span>
                        ) : (
                          <span className="text-muted-foreground text-sm">Not processed</span>
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <Eye className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            {hasPermission('repayments.view_details') && (
                              <DropdownMenuItem onClick={() => handleViewRepayment(repayment)}>
                                <Eye className="mr-2 h-4 w-4" />
                                View Details
                              </DropdownMenuItem>
                            )}
                            {hasPermission('repayments.export') && (
                              <DropdownMenuItem>
                                <Download className="mr-2 h-4 w-4" />
                                Export Cycle
                              </DropdownMenuItem>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* Pagination */}
              <div className="flex items-center justify-between px-2 py-4">
                <div className="flex items-center space-x-2">
                  <p className="text-sm font-medium">
                    Showing {startIndex + 1} to {Math.min(endIndex, totalItems)} of {totalItems}{' '}
                    entries
                  </p>
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(currentPage - 1)}
                    disabled={currentPage === 1}
                  >
                    Previous
                  </Button>
                  <div className="flex items-center space-x-1">
                    {Array.from({ length: totalPages }, (_, i) => i + 1)
                      .filter((page) => {
                        return (
                          page === 1 ||
                          page === totalPages ||
                          (page >= currentPage - 1 && page <= currentPage + 1)
                        );
                      })
                      .map((page, index, array) => (
                        <div key={page} className="flex items-center">
                          {index > 0 && array[index - 1] !== page - 1 && (
                            <span className="text-muted-foreground px-2 text-sm">...</span>
                          )}
                          <Button
                            variant={currentPage === page ? 'default' : 'outline'}
                            size="sm"
                            onClick={() => setCurrentPage(page)}
                            className={currentPage === page ? 'bg-primary hover:bg-primary/90' : ''}
                          >
                            {page}
                          </Button>
                        </div>
                      ))}
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(currentPage + 1)}
                    disabled={currentPage === totalPages}
                  >
                    Next
                  </Button>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
