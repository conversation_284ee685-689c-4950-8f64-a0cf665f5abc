'use client';

import { useState } from 'react';
import {
  Plus,
  Search,
  MoreHorizontal,
  Edit,
  Trash2,
  ChevronLeft,
  ChevronRight,
  DollarSign,
  Calendar,
  User,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { AddWageModal } from './add-wage-modal';
import { usePermissions } from '@/lib/permissions';

// Sample wages data (removed status)
const wagesData = [
  {
    id: 1,
    employeeId: 1,
    employeeName: '<PERSON>',
    employeeEmail: '<EMAIL>',
    amount: 150.0,
    date: '2024-01-15',
    createdAt: '2024-01-15T10:30:00Z',
  },
  {
    id: 2,
    employeeId: 1,
    employeeName: 'Alice Johnson',
    employeeEmail: '<EMAIL>',
    amount: 175.5,
    date: '2024-01-16',
    createdAt: '2024-01-16T09:15:00Z',
  },
  {
    id: 3,
    employeeId: 2,
    employeeName: 'Bob Smith',
    employeeEmail: '<EMAIL>',
    amount: 200.0,
    date: '2024-01-15',
    createdAt: '2024-01-15T11:45:00Z',
  },
  {
    id: 4,
    employeeId: 2,
    employeeName: 'Bob Smith',
    employeeEmail: '<EMAIL>',
    amount: 180.25,
    date: '2024-01-16',
    createdAt: '2024-01-16T08:30:00Z',
  },
  {
    id: 5,
    employeeId: 3,
    employeeName: 'Carol Davis',
    employeeEmail: '<EMAIL>',
    amount: 165.75,
    date: '2024-01-15',
    createdAt: '2024-01-15T14:20:00Z',
  },
  {
    id: 6,
    employeeId: 4,
    employeeName: 'David Wilson',
    employeeEmail: '<EMAIL>',
    amount: 190.0,
    date: '2024-01-17',
    createdAt: '2024-01-17T10:00:00Z',
  },
  {
    id: 7,
    employeeId: 5,
    employeeName: 'Emma Brown',
    employeeEmail: '<EMAIL>',
    amount: 220.5,
    date: '2024-01-18',
    createdAt: '2024-01-18T11:30:00Z',
  },
  {
    id: 8,
    employeeId: 3,
    employeeName: 'Carol Davis',
    employeeEmail: '<EMAIL>',
    amount: 145.0,
    date: '2024-01-19',
    createdAt: '2024-01-19T09:45:00Z',
  },
];

export function WageManagement() {
  const { hasPermission } = usePermissions();
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [isAddWageModalOpen, setIsAddWageModalOpen] = useState(false);
  const [monthFilter, setMonthFilter] = useState('all');

  // Month options for filter
  const monthOptions = [
    { value: 'all', label: 'All Months' },
    { value: '01', label: 'January' },
    { value: '02', label: 'February' },
    { value: '03', label: 'March' },
    { value: '04', label: 'April' },
    { value: '05', label: 'May' },
    { value: '06', label: 'June' },
    { value: '07', label: 'July' },
    { value: '08', label: 'August' },
    { value: '09', label: 'September' },
    { value: '10', label: 'October' },
    { value: '11', label: 'November' },
    { value: '12', label: 'December' },
  ];

  // Apply search and month filters (removed status filter)
  const filteredWages = wagesData.filter((wage) => {
    const matchesSearch =
      wage.employeeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      wage.employeeEmail.toLowerCase().includes(searchTerm.toLowerCase()) ||
      wage.amount.toString().includes(searchTerm) ||
      wage.date.includes(searchTerm);
    const matchesMonth = monthFilter === 'all' || wage.date.split('-')[1] === monthFilter;
    return matchesSearch && matchesMonth;
  });

  // Pagination calculations
  const totalItems = filteredWages.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentWages = filteredWages.slice(startIndex, endIndex);

  // Reset pagination when filters change
  const handleFilterChange = () => {
    setCurrentPage(1);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Calculate statistics (removed status-based calculations)
  const totalWages = wagesData.reduce((sum, wage) => sum + wage.amount, 0);
  const averageWage = wagesData.length > 0 ? totalWages / wagesData.length : 0;
  const highestWage = wagesData.length > 0 ? Math.max(...wagesData.map((w) => w.amount)) : 0;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight text-gray-900">Wage Management</h2>
          <p className="text-muted-foreground text-lg">Manage employee wages and payments</p>
        </div>
        {hasPermission('wages.create') && (
          <Button
            onClick={() => setIsAddWageModalOpen(true)}
            className="bg-primary hover:bg-primary/90 h-10 px-6"
          >
            <Plus className="mr-2 h-4 w-4" />
            Add Wage
          </Button>
        )}
      </div>

      {/* Stats Cards (removed status-based stats) */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="bg-primary/10 flex h-12 w-12 items-center justify-center rounded-lg">
                <DollarSign className="text-primary h-6 w-6" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Wages</p>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(totalWages)}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-100">
                <DollarSign className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Average Wage</p>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(averageWage)}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-yellow-100">
                <DollarSign className="h-6 w-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Highest Wage</p>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(highestWage)}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-100">
                <User className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Entries</p>
                <p className="text-2xl font-bold text-gray-900">{wagesData.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Wages List */}
      <Card>
        <CardHeader className="pb-6">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-xl">All Wages</CardTitle>
              <CardDescription className="text-base">
                Complete list of employee wages and payments
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Filters and Search (removed status filter) */}
          <div className="mb-6 flex flex-col gap-4 sm:flex-row">
            <div className="relative max-w-md flex-1">
              <Search className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2" />
              <Input
                placeholder="Search by employee, amount, or date..."
                value={searchTerm}
                onChange={(e) => {
                  setSearchTerm(e.target.value);
                  handleFilterChange();
                }}
                className="h-10 pl-10"
              />
            </div>
            <div className="flex gap-3">
              <Select
                value={monthFilter}
                onValueChange={(value) => {
                  setMonthFilter(value);
                  handleFilterChange();
                }}
              >
                <SelectTrigger className="h-10 w-[160px]">
                  <SelectValue placeholder="Month" />
                </SelectTrigger>
                <SelectContent>
                  {monthOptions.map((month) => (
                    <SelectItem key={month.value} value={month.value}>
                      {month.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select
                value={itemsPerPage.toString()}
                onValueChange={(value) => {
                  setItemsPerPage(Number.parseInt(value));
                  setCurrentPage(1);
                }}
              >
                <SelectTrigger className="h-10 w-[120px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">5 rows</SelectItem>
                  <SelectItem value="10">10 rows</SelectItem>
                  <SelectItem value="20">20 rows</SelectItem>
                  <SelectItem value="50">50 rows</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Wages Table (removed status column) */}
          <div className="overflow-hidden rounded-lg border border-gray-200">
            <Table>
              <TableHeader>
                <TableRow className="bg-gray-50">
                  <TableHead className="font-semibold text-gray-900">Employee</TableHead>
                  <TableHead className="font-semibold text-gray-900">Date</TableHead>
                  <TableHead className="font-semibold text-gray-900">Amount</TableHead>
                  <TableHead className="font-semibold text-gray-900">Created</TableHead>
                  <TableHead className="text-right font-semibold text-gray-900">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {currentWages.map((wage) => (
                  <TableRow key={wage.id} className="hover:bg-gray-50">
                    <TableCell className="py-4">
                      <div className="flex items-center gap-3">
                        <Avatar className="h-10 w-10">
                          <AvatarImage src={`/placeholder.svg?height=40&width=40`} />
                          <AvatarFallback className="bg-primary/10 text-primary font-medium">
                            {wage.employeeName
                              .split(' ')
                              .map((n) => n[0])
                              .join('')}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium text-gray-900">{wage.employeeName}</div>
                          <div className="text-sm text-gray-600">{wage.employeeEmail}</div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="py-4">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-gray-400" />
                        <span className="font-medium text-gray-900">{formatDate(wage.date)}</span>
                      </div>
                    </TableCell>
                    <TableCell className="py-4">
                      <div className="flex items-center gap-2">
                        <DollarSign className="h-4 w-4 text-gray-400" />
                        <span className="font-semibold text-gray-900">
                          {formatCurrency(wage.amount)}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell className="py-4">
                      <span className="text-sm text-gray-600">{formatDate(wage.createdAt)}</span>
                    </TableCell>
                    <TableCell className="py-4 text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <span className="sr-only">Open menu</span>
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          {hasPermission('wages.edit') && (
                            <DropdownMenuItem>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit Wage
                            </DropdownMenuItem>
                          )}
                          {hasPermission('wages.delete') && (
                            <>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem className="text-red-600">
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete Wage
                              </DropdownMenuItem>
                            </>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          <div className="flex items-center justify-between px-2 py-4">
            <div className="flex items-center space-x-2">
              <p className="text-sm font-medium text-gray-700">
                Showing {startIndex + 1} to {Math.min(endIndex, totalItems)} of {totalItems} entries
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(currentPage - 1)}
                disabled={currentPage === 1}
              >
                <ChevronLeft className="h-4 w-4" />
                Previous
              </Button>
              <div className="flex items-center space-x-1">
                {Array.from({ length: totalPages }, (_, i) => i + 1)
                  .filter((page) => {
                    return (
                      page === 1 ||
                      page === totalPages ||
                      (page >= currentPage - 1 && page <= currentPage + 1)
                    );
                  })
                  .map((page, index, array) => (
                    <div key={page} className="flex items-center">
                      {index > 0 && array[index - 1] !== page - 1 && (
                        <span className="text-muted-foreground px-2 text-sm">...</span>
                      )}
                      <Button
                        variant={currentPage === page ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setCurrentPage(page)}
                        className={currentPage === page ? 'bg-primary hover:bg-primary/90' : ''}
                      >
                        {page}
                      </Button>
                    </div>
                  ))}
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(currentPage + 1)}
                disabled={currentPage === totalPages}
              >
                Next
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {filteredWages.length === 0 && (
            <div className="py-12 text-center">
              <div className="flex flex-col items-center gap-2">
                <DollarSign className="h-12 w-12 text-gray-400" />
                <p className="text-lg font-medium text-gray-900">No wages found</p>
                <p className="text-sm text-gray-600">
                  Try adjusting your search criteria or add a new wage entry.
                </p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add Wage Modal */}
      {hasPermission('wages.create') && (
        <AddWageModal isOpen={isAddWageModalOpen} onClose={() => setIsAddWageModalOpen(false)} />
      )}
    </div>
  );
}
