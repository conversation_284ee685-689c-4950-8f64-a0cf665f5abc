'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Users, DollarSign, Clock, TrendingUp, Plus } from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';

export function EmployerDashboardContent() {
  const stats = [
    {
      title: 'Total Employees',
      value: '156',
      change: '+12%',
      changeType: 'positive' as const,
      icon: Users,
    },
    {
      title: 'Monthly Payroll',
      value: '$45,230',
      change: '+8.2%',
      changeType: 'positive' as const,
      icon: DollarSign,
    },
    {
      title: 'Pending Requests',
      value: '23',
      change: '-5%',
      changeType: 'negative' as const,
      icon: Clock,
    },
    {
      title: 'Growth Rate',
      value: '12.5%',
      change: '+2.1%',
      changeType: 'positive' as const,
      icon: TrendingUp,
    },
  ];

  const recentRequests = [
    {
      id: 1,
      employee: '<PERSON>',
      amount: '$2,500',
      status: 'pending',
      date: '2024-01-15',
    },
    {
      id: 2,
      employee: '<PERSON>',
      amount: '$3,200',
      status: 'approved',
      date: '2024-01-14',
    },
    {
      id: 3,
      employee: 'Mike <PERSON>',
      amount: '$1,800',
      status: 'pending',
      date: '2024-01-13',
    },
    {
      id: 4,
      employee: 'Sarah Wilson',
      amount: '$2,900',
      status: 'rejected',
      date: '2024-01-12',
    },
    {
      id: 5,
      employee: 'Tom <PERSON>',
      amount: '$2,100',
      status: 'approved',
      date: '2024-01-11',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Stats Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => (
          <Card key={stat.title}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
              <stat.icon className="text-muted-foreground h-4 w-4" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <p
                className={`text-xs ${stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'}`}
              >
                {stat.change} from last month
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Recent Salary Requests */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Recent Salary Requests</CardTitle>
                <CardDescription>Latest employee salary advance requests</CardDescription>
              </div>
              <Button size="sm">
                <Plus className="mr-2 h-4 w-4" />
                View All
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-[300px]">
              <div className="space-y-4">
                {recentRequests.map((request) => (
                  <div
                    key={request.id}
                    className="flex items-center justify-between rounded-lg border p-3"
                  >
                    <div className="flex-1">
                      <p className="font-medium">{request.employee}</p>
                      <p className="text-sm text-gray-500">{request.date}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold">{request.amount}</p>
                      <Badge
                        variant={
                          request.status === 'approved'
                            ? 'default'
                            : request.status === 'pending'
                              ? 'secondary'
                              : 'destructive'
                        }
                        className="text-xs"
                      >
                        {request.status}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>Common tasks and shortcuts</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-3">
              <Button className="h-12 justify-start bg-transparent" variant="outline">
                <Users className="mr-3 h-4 w-4" />
                Add New Employee
              </Button>
              <Button className="h-12 justify-start bg-transparent" variant="outline">
                <DollarSign className="mr-3 h-4 w-4" />
                Process Payroll
              </Button>
              <Button className="h-12 justify-start bg-transparent" variant="outline">
                <Clock className="mr-3 h-4 w-4" />
                Review Requests
              </Button>
              <Button className="h-12 justify-start bg-transparent" variant="outline">
                <TrendingUp className="mr-3 h-4 w-4" />
                View Reports
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Activity Chart Placeholder */}
      <Card>
        <CardHeader>
          <CardTitle>Employee Activity Overview</CardTitle>
          <CardDescription>Monthly employee and payroll trends</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex h-[300px] items-center justify-center rounded-lg border-2 border-dashed border-gray-300">
            <div className="text-center">
              <TrendingUp className="mx-auto mb-4 h-12 w-12 text-gray-400" />
              <p className="text-gray-500">Activity charts will be displayed here</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
