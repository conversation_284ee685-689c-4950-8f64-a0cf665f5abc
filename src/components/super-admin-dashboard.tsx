'use client';
import { useState } from 'react';
import {
  Users,
  Building2,
  DollarSign,
  TrendingUp,
  Plus,
  FileText,
  UserX,
  Eye,
  Calendar,
  Activity,
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, ResponsiveContainer } from 'recharts';

// Sample data for top employers by employee count
const topEmployersData = [
  { name: 'TechCorp Inc.', employees: 450 },
  { name: 'Global Solutions', employees: 380 },
  { name: 'Innovation Labs', employees: 320 },
  { name: 'Digital Dynamics', employees: 285 },
  { name: 'Future Systems', employees: 240 },
];

export function SuperAdminDashboard() {
  const [timeRange, setTimeRange] = useState('12m');

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Super Admin Dashboard</h2>
          <p className="text-muted-foreground text-sm">
            Monitor platform-wide activity and performance
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Calendar className="mr-2 h-4 w-4" />
            Last 12 months
          </Button>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-4">
        <Card className="border border-gray-200 shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Employers</CardTitle>
            <Building2 className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent className="pb-2">
            <div className="text-xl font-bold">1,247</div>
            <p className="text-muted-foreground text-xs">
              <span className="flex items-center text-green-600">
                <TrendingUp className="mr-1 h-3 w-3" />
                +12.5%
              </span>
              from last month
            </p>
          </CardContent>
        </Card>

        <Card className="border border-gray-200 shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Employees</CardTitle>
            <Users className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent className="pb-2">
            <div className="text-xl font-bold">45,892</div>
            <p className="text-muted-foreground text-xs">
              <span className="flex items-center text-green-600">
                <TrendingUp className="mr-1 h-3 w-3" />
                +8.2%
              </span>
              from last month
            </p>
          </CardContent>
        </Card>

        <Card className="border border-gray-200 shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Wage Advances</CardTitle>
            <Activity className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent className="pb-2">
            <div className="text-xl font-bold">23,456</div>
            <p className="text-muted-foreground text-xs">
              <span className="flex items-center text-green-600">
                <TrendingUp className="mr-1 h-3 w-3" />
                +15.3%
              </span>
              from last month
            </p>
          </CardContent>
        </Card>

        <Card className="border border-gray-200 shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Disbursed Amount</CardTitle>
            <DollarSign className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent className="pb-2">
            <div className="text-xl font-bold">$2.4M</div>
            <p className="text-muted-foreground text-xs">
              <span className="flex items-center text-green-600">
                <TrendingUp className="mr-1 h-3 w-3" />
                +22.8%
              </span>
              from last month
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Top Employers Chart */}
      <Card className="border border-gray-200 shadow-sm">
        <CardHeader className="pb-2">
          <CardTitle className="text-base">Top Employers by Employee Count</CardTitle>
          <CardDescription className="text-xs">
            Companies with the highest number of registered employees
          </CardDescription>
        </CardHeader>
        <CardContent className="pb-2">
          <ChartContainer
            config={{
              employees: {
                label: 'Employees',
                color: 'hsl(var(--chart-2))',
              },
            }}
            className="h-[160px]"
          >
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={topEmployersData} layout="horizontal">
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis type="number" fontSize={10} />
                <YAxis dataKey="name" type="category" width={100} fontSize={10} />
                <ChartTooltip content={<ChartTooltipContent />} />
                <Bar dataKey="employees" fill="var(--color-employees)" radius={[0, 3, 3, 0]} />
              </BarChart>
            </ResponsiveContainer>
          </ChartContainer>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <div className="grid gap-4 md:grid-cols-1">
        <Card className="border border-gray-200 shadow-sm">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Quick Actions</CardTitle>
            <CardDescription className="text-sm">
              Frequently used administrative actions
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-2 pb-3">
            <Button className="h-9 w-full justify-start bg-blue-600 hover:bg-blue-700">
              <Plus className="mr-2 h-4 w-4" />
              Add New Employer
            </Button>
            <Button variant="outline" className="h-9 w-full justify-start">
              <Eye className="mr-2 h-4 w-4" />
              View All Employers
            </Button>
            <Button variant="outline" className="h-9 w-full justify-start">
              <FileText className="mr-2 h-4 w-4" />
              Generate Reports
            </Button>
            <Button
              variant="outline"
              className="h-9 w-full justify-start border-red-200 text-red-600 hover:bg-red-50"
            >
              <UserX className="mr-2 h-4 w-4" />
              Manage Suspended Accounts
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
