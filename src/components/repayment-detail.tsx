'use client';

import { useState } from 'react';
import {
  ArrowLeft,
  Search,
  Eye,
  Calendar,
  Users,
  DollarSign,
  TrendingUp,
  FileText,
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

// Sample employee requests data
const employeeRequests = [
  {
    id: 'EMP001',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: '/placeholder.svg?height=32&width=32',
    department: 'Engineering',
    position: 'Senior Developer',
    requestedAmount: 2500,
    requestDate: '2024-01-15',
    reason: 'Medical emergency',
  },
  {
    id: 'EMP002',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: '/placeholder.svg?height=32&width=32',
    department: 'Marketing',
    position: 'Marketing Manager',
    requestedAmount: 3000,
    requestDate: '2024-01-16',
    reason: 'Home renovation',
  },
  {
    id: 'EMP003',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: '/placeholder.svg?height=32&width=32',
    department: 'Sales',
    position: 'Sales Representative',
    requestedAmount: 1800,
    requestDate: '2024-01-17',
    reason: 'Car repair',
  },
  {
    id: 'EMP004',
    name: 'Emily Davis',
    email: '<EMAIL>',
    avatar: '/placeholder.svg?height=32&width=32',
    department: 'HR',
    position: 'HR Specialist',
    requestedAmount: 2200,
    requestDate: '2024-01-18',
    reason: 'Education expenses',
  },
  {
    id: 'EMP005',
    name: 'David Wilson',
    email: '<EMAIL>',
    avatar: '/placeholder.svg?height=32&width=32',
    department: 'Finance',
    position: 'Financial Analyst',
    requestedAmount: 2800,
    requestDate: '2024-01-19',
    reason: 'Family emergency',
  },
  {
    id: 'EMP006',
    name: 'Lisa Anderson',
    email: '<EMAIL>',
    avatar: '/placeholder.svg?height=32&width=32',
    department: 'Operations',
    position: 'Operations Manager',
    requestedAmount: 3200,
    requestDate: '2024-01-20',
    reason: 'Debt consolidation',
  },
  {
    id: 'EMP007',
    name: 'Robert Taylor',
    email: '<EMAIL>',
    avatar: '/placeholder.svg?height=32&width=32',
    department: 'IT',
    position: 'System Administrator',
    requestedAmount: 1900,
    requestDate: '2024-01-21',
    reason: 'Moving expenses',
  },
  {
    id: 'EMP008',
    name: 'Jennifer Martinez',
    email: '<EMAIL>',
    avatar: '/placeholder.svg?height=32&width=32',
    department: 'Legal',
    position: 'Legal Counsel',
    requestedAmount: 2600,
    requestDate: '2024-01-22',
    reason: 'Medical bills',
  },
];

interface RepaymentDetailProps {
  repayment: any;
  onBack: () => void;
}

export function RepaymentDetail({ repayment, onBack }: RepaymentDetailProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(5);

  // Calculate financial metrics
  const totalAmountRequested = employeeRequests.reduce((sum, req) => sum + req.requestedAmount, 0);
  const totalRequests = employeeRequests.length;
  const commissionRate = 0.002; // 0.2%
  const totalCommission = totalAmountRequested * commissionRate;
  const repaymentAmount = totalAmountRequested - totalCommission;

  // Filter requests based on search term
  const filteredRequests = employeeRequests.filter(
    (request) =>
      request.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.department.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.reason.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Pagination calculations
  const totalItems = filteredRequests.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentRequests = filteredRequests.slice(startIndex, endIndex);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="outline" size="sm" onClick={onBack}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Repayments
        </Button>
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Repayment Details</h2>
          <p className="text-muted-foreground text-sm">
            Detailed breakdown for {repayment.payrollCycle}
          </p>
        </div>
      </div>

      {/* Cycle Information Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5 text-blue-600" />
            Payroll Cycle Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
            <div>
              <p className="text-sm font-medium text-gray-500">Cycle Period</p>
              <p className="text-lg font-semibold">{repayment.payrollCycle}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Date Range</p>
              <p className="text-lg font-semibold">{repayment.dateRange}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Status</p>
              <Badge
                variant={
                  repayment.status === 'Completed'
                    ? 'default'
                    : repayment.status === 'Processing'
                      ? 'secondary'
                      : repayment.status === 'Pending'
                        ? 'destructive'
                        : 'outline'
                }
                className="mt-1"
              >
                {repayment.status}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Financial Breakdown */}
      <Card className="border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-blue-800">
            <DollarSign className="h-5 w-5" />
            Financial Breakdown
          </CardTitle>
          <CardDescription className="text-blue-600">
            Complete breakdown of repayment calculations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
            <div className="rounded-lg border border-blue-200 bg-white p-4">
              <div className="flex items-center gap-2">
                <TrendingUp className="h-4 w-4 text-green-600" />
                <p className="text-sm font-medium text-gray-600">Total Amount Req</p>
              </div>
              <p className="text-2xl font-bold text-gray-900">
                ${totalAmountRequested.toLocaleString()}
              </p>
            </div>
            <div className="rounded-lg border border-blue-200 bg-white p-4">
              <div className="flex items-center gap-2">
                <Users className="h-4 w-4 text-blue-600" />
                <p className="text-sm font-medium text-gray-600">Total Req</p>
              </div>
              <p className="text-2xl font-bold text-gray-900">{totalRequests}</p>
            </div>
            <div className="rounded-lg border border-blue-200 bg-white p-4">
              <div className="flex items-center gap-2">
                <FileText className="h-4 w-4 text-orange-600" />
                <p className="text-sm font-medium text-gray-600">Total Commission</p>
              </div>
              <p className="text-2xl font-bold text-gray-900">${totalCommission.toFixed(0)}</p>
            </div>
            <div className="rounded-lg border border-blue-200 bg-white p-4">
              <div className="flex items-center gap-2">
                <DollarSign className="h-4 w-4 text-purple-600" />
                <p className="text-sm font-medium text-gray-600">Repayment Amount</p>
              </div>
              <p className="text-2xl font-bold text-gray-900">${repaymentAmount.toFixed(0)}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Employee Requests */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Employee Requests</CardTitle>
              <CardDescription>
                List of salary advance requests from employees in this cycle
              </CardDescription>
            </div>
            <div className="flex items-center gap-4">
              <div className="relative">
                <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 text-gray-400" />
                <Input
                  placeholder="Search employees..."
                  value={searchTerm}
                  onChange={(e) => {
                    setSearchTerm(e.target.value);
                    setCurrentPage(1);
                  }}
                  className="w-80 pl-10"
                />
              </div>
              <Select
                value={itemsPerPage.toString()}
                onValueChange={(value) => {
                  setItemsPerPage(Number.parseInt(value));
                  setCurrentPage(1);
                }}
              >
                <SelectTrigger className="w-[120px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">5 rows</SelectItem>
                  <SelectItem value="10">10 rows</SelectItem>
                  <SelectItem value="20">20 rows</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {currentRequests.length === 0 ? (
            <div className="py-8 text-center">
              <Users className="mx-auto mb-4 h-12 w-12 text-gray-400" />
              <h3 className="mb-2 text-lg font-medium text-gray-900">No requests found</h3>
              <p className="text-gray-500">
                {searchTerm
                  ? 'Try adjusting your search criteria'
                  : 'No employee requests in this cycle'}
              </p>
            </div>
          ) : (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Employee</TableHead>
                    <TableHead>Department</TableHead>
                    <TableHead>Position</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Request Date</TableHead>
                    <TableHead>Reason</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {currentRequests.map((request) => (
                    <TableRow key={request.id}>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={request.avatar || '/placeholder.svg'} />
                            <AvatarFallback>
                              {request.name
                                .split(' ')
                                .map((n) => n[0])
                                .join('')}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium">{request.name}</p>
                            <p className="text-muted-foreground text-sm">{request.email}</p>
                            <p className="text-muted-foreground text-xs">ID: {request.id}</p>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <span className="font-medium">{request.department}</span>
                      </TableCell>
                      <TableCell>
                        <span className="text-sm">{request.position}</span>
                      </TableCell>
                      <TableCell>
                        <span className="font-semibold text-green-600">
                          ${request.requestedAmount.toLocaleString()}
                        </span>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-gray-400" />
                          <span className="text-sm">{request.requestDate}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <span className="text-sm">{request.reason}</span>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* Pagination */}
              <div className="flex items-center justify-between px-2 py-4">
                <div className="flex items-center space-x-2">
                  <p className="text-sm font-medium">
                    Showing {startIndex + 1} to {Math.min(endIndex, totalItems)} of {totalItems}{' '}
                    entries
                  </p>
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(currentPage - 1)}
                    disabled={currentPage === 1}
                  >
                    Previous
                  </Button>
                  <div className="flex items-center space-x-1">
                    {Array.from({ length: totalPages }, (_, i) => i + 1)
                      .filter((page) => {
                        return (
                          page === 1 ||
                          page === totalPages ||
                          (page >= currentPage - 1 && page <= currentPage + 1)
                        );
                      })
                      .map((page, index, array) => (
                        <div key={page} className="flex items-center">
                          {index > 0 && array[index - 1] !== page - 1 && (
                            <span className="text-muted-foreground px-2 text-sm">...</span>
                          )}
                          <Button
                            variant={currentPage === page ? 'default' : 'outline'}
                            size="sm"
                            onClick={() => setCurrentPage(page)}
                            className={currentPage === page ? 'bg-primary hover:bg-primary/90' : ''}
                          >
                            {page}
                          </Button>
                        </div>
                      ))}
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(currentPage + 1)}
                    disabled={currentPage === totalPages}
                  >
                    Next
                  </Button>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
