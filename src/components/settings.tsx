'use client';
import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> } from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

export function Settings() {
  const [defaultLimit, setDefaultLimit] = useState(50);
  const [emailNotifications, setEmailNotifications] = useState(true);
  const [smsNotifications, setSmsNotifications] = useState(false);

  const handleSaveSettings = () => {
    // Here you would typically save to your backend
    console.log('Saving settings:', {
      defaultLimit,
      emailNotifications,
      smsNotifications,
    });
    // Show success message
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Settings</h2>
          <p className="text-muted-foreground text-sm">
            Configure system-wide settings and salary advance limits
          </p>
        </div>
      </div>

      <Tabs defaultValue="salary-limits" className="space-y-4">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="salary-limits">Salary Limits</TabsTrigger>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
        </TabsList>

        {/* Salary Limits Tab */}
        <TabsContent value="salary-limits" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Percent className="h-5 w-5 text-blue-600" />
                Salary Advance Limits
              </CardTitle>
              <CardDescription>
                Set the default salary advance limit that applies to all employers
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="default-limit">Default Advance Limit (%)</Label>
                <Input
                  id="default-limit"
                  type="number"
                  min="0"
                  max="100"
                  value={defaultLimit}
                  onChange={(e) => setDefaultLimit(Number(e.target.value))}
                  className="w-full max-w-xs"
                />
                <p className="text-muted-foreground text-xs">
                  Percentage of earned salary that employees can request as advance
                </p>
              </div>

              <div className="flex justify-end">
                <Button onClick={handleSaveSettings} className="bg-primary hover:bg-primary/90">
                  <Save className="mr-2 h-4 w-4" />
                  Save Settings
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Notifications Tab */}
        <TabsContent value="notifications" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="h-5 w-5 text-orange-600" />
                Notification Settings
              </CardTitle>
              <CardDescription>Configure how and when notifications are sent</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label className="flex items-center gap-2">
                      <Mail className="h-4 w-4" />
                      Email Notifications
                    </Label>
                    <p className="text-muted-foreground text-sm">
                      Send email notifications for salary advance requests and approvals
                    </p>
                  </div>
                  <Switch checked={emailNotifications} onCheckedChange={setEmailNotifications} />
                </div>

                <Separator />

                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label>SMS Notifications</Label>
                    <p className="text-muted-foreground text-sm">
                      Send SMS notifications for urgent salary advance requests
                    </p>
                  </div>
                  <Switch checked={smsNotifications} onCheckedChange={setSmsNotifications} />
                </div>
              </div>

              <div className="flex justify-end">
                <Button onClick={handleSaveSettings} className="bg-primary hover:bg-primary/90">
                  <Save className="mr-2 h-4 w-4" />
                  Save Notification Settings
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
